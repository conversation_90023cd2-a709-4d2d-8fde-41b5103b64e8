.feedBack {
  position: absolute;
  cursor: pointer;
  right: 18px;
  bottom: 34px;
  display: inline-flex;
  gap: 6px;
  padding: 0 13px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  border-radius: 77px;
  outline: 1px solid #e8ebed;
  box-shadow: 0 18px 36px 0 rgba(0, 0, 0, 0.08);

  &:hover,
  &:active {
    background: var(---, rgba(0, 0, 0, 0.06));
    box-shadow: 0 18px 36px 0 rgba(0, 0, 0, 0.08);
  }

  .text,
  .enText {
    width: 16px;
    color: rgba(0, 0, 0, 0.88);
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
  }
  .enText {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    height: fit-content;
    width: fit-content;
  }
}

.feedbackDropdown {
  background: #fff;
  border-radius: 8px;
  box-shadow:
    0px 9px 28px 8px rgba(0, 0, 0, 0.05),
    0px 3px 6px -4px rgba(0, 0, 0, 0.12),
    0px 6px 16px 0px rgba(0, 0, 0, 0.08);
  padding: 16px 24px 24px;
  overflow: hidden;
  position: relative;
  width: 520px;
}

.closeIcon {
  position: absolute;
  top: 30px;
  right: 28px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.45);
  transition: color 0.2s ease;

  &:hover {
    color: rgba(0, 0, 0, 0.88);
  }
}

.title {
  color: rgba(0, 0, 0, 0.88);
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.subDesc {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.ratingSection {
  margin: 20px 0 24px 0;
}

.ratingLabel {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 12px;
}

.required {
  color: #f15a4d;
}

.ratingButtons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.ratingButton {
  width: 40px;
  height: 40px;
  background: #f0f6ff;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #3d68f5;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;

  &:hover {
    background: #BAD1FF;
  }
}

.ratingButtonActive {
  background: #3d68f5;
  color: white;
}

.suggestionSection {
  margin-bottom: 12px;
}

.suggestionLabel {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 12px;
}

.suggestionTextarea {
  width: calc(100% - 24px);
  min-height: 80px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.88);
  resize: vertical;
  outline: none;
  transition: border-color 0.2s ease;
  padding: 2px 12px;

  &:hover {
    border-color: #3D68F5;
  }

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &::placeholder {
    color: rgba(0, 0, 0, 0.45);
  }
}

.buttonGroup {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.thankYou {
  text-align: center;
  padding: 48px 0;
}

.thankYouText {
  color: rgba(0, 0, 0, 0.88);
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
}
